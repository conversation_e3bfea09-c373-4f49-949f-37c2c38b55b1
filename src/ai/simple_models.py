from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
import sqlite3
import logging
import pytz
from src.utils.database import db_manager

logger = logging.getLogger(__name__)


@dataclass
class SimpleReminder:
    id: Optional[int]
    user_id: int
    message_text: str
    scheduled_time_utc: datetime
    created_at: Optional[datetime] = None
    is_sent: bool = False
    is_cancelled: bool = False
    scheduler_job_id: Optional[str] = None


class SimpleReminderManager:
    @staticmethod
    def create_reminder(user_id: int, message_text: str, scheduled_time_utc: datetime) -> Optional[int]:
        """Create a new reminder and return its ID."""
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO reminders (user_id, message_text, scheduled_time_utc) 
                    VALUES (?, ?, ?)
                """, (user_id, message_text, scheduled_time_utc.isoformat()))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error creating reminder: {e}")
            return None
    
    @staticmethod
    def get_reminder(reminder_id: int) -> Optional[SimpleReminder]:
        """Get reminder by ID."""
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM reminders WHERE id = ?", (reminder_id,))
            result = cursor.fetchone()
            
            if result:
                return SimpleReminder(
                    id=result['id'],
                    user_id=result['user_id'],
                    message_text=result['message_text'],
                    scheduled_time_utc=datetime.fromisoformat(result['scheduled_time_utc']),
                    created_at=datetime.fromisoformat(result['created_at']) if result['created_at'] else None,
                    is_sent=bool(result['is_sent']),
                    is_cancelled=bool(result['is_cancelled']),
                    scheduler_job_id=result['scheduler_job_id']
                )
            return None
    
    @staticmethod
    def get_user_reminders(user_id: int, status: str = "pending") -> List[SimpleReminder]:
        """Get user's reminders filtered by status."""
        reminders = []
        
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            if status == "pending":
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ? AND is_sent = FALSE AND is_cancelled = FALSE
                    ORDER BY scheduled_time_utc ASC
                """, (user_id,))
            elif status == "completed":
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ? AND is_sent = TRUE
                    ORDER BY scheduled_time_utc DESC
                """, (user_id,))
            elif status == "cancelled":
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ? AND is_cancelled = TRUE
                    ORDER BY scheduled_time_utc DESC
                """, (user_id,))
            else:  # all
                cursor.execute("""
                    SELECT * FROM reminders 
                    WHERE user_id = ?
                    ORDER BY scheduled_time_utc DESC
                """, (user_id,))
            
            for result in cursor.fetchall():
                reminders.append(SimpleReminder(
                    id=result['id'],
                    user_id=result['user_id'],
                    message_text=result['message_text'],
                    scheduled_time_utc=datetime.fromisoformat(result['scheduled_time_utc']),
                    created_at=datetime.fromisoformat(result['created_at']) if result['created_at'] else None,
                    is_sent=bool(result['is_sent']),
                    is_cancelled=bool(result['is_cancelled']),
                    scheduler_job_id=result['scheduler_job_id']
                ))
        
        return reminders
    
    @staticmethod
    def update_reminder(reminder_id: int, **kwargs) -> bool:
        """Update reminder fields."""
        if not kwargs:
            return False
        
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Build dynamic UPDATE query
                fields = []
                values = []
                for key, value in kwargs.items():
                    if key in ['message_text', 'scheduled_time_utc', 'is_sent', 'is_cancelled', 'scheduler_job_id']:
                        fields.append(f"{key} = ?")
                        if key == 'scheduled_time_utc' and isinstance(value, datetime):
                            values.append(value.isoformat())
                        else:
                            values.append(value)
                
                if fields:
                    query = f"UPDATE reminders SET {', '.join(fields)} WHERE id = ?"
                    values.append(reminder_id)
                    cursor.execute(query, values)
                    conn.commit()
                    return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error updating reminder {reminder_id}: {e}")
        
        return False
    
    @staticmethod
    def delete_reminder(reminder_id: int) -> bool:
        """Delete reminder."""
        try:
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM reminders WHERE id = ?", (reminder_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error deleting reminder {reminder_id}: {e}")
            return False
    
    @staticmethod
    def mark_as_sent(reminder_id: int) -> bool:
        """Mark reminder as sent."""
        return SimpleReminderManager.update_reminder(reminder_id, is_sent=True)
    
    @staticmethod
    def cancel_reminder(reminder_id: int) -> bool:
        """Cancel reminder."""
        return SimpleReminderManager.update_reminder(reminder_id, is_cancelled=True)