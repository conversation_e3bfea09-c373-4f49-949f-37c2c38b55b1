from agno.tools import tool
from datetime import datetime, timedelta
from typing import Optional
import pytz
import re
import logging

from src.ai.simple_models import SimpleReminderManager
from src.utils.scheduler import reminder_scheduler

logger = logging.getLogger(__name__)

# Use Poland timezone for all users
POLAND_TZ = "Europe/Warsaw"

def parse_date_time(date_str: str, time_str: str) -> Optional[datetime]:
    """Parse date and time strings into timezone-aware datetime."""
    try:
        # Parse date - use Poland timezone for "today"
        poland_tz = pytz.timezone(POLAND_TZ)
        today_poland = datetime.now(poland_tz).replace(tzinfo=None)  # Remove timezone for date manipulation
        
        date_patterns = [
            (r'^today$', lambda: today_poland),
            (r'^tomorrow$', lambda: today_poland + timedelta(days=1)),
            (r'^(\d{1,2})/(\d{1,2})$', lambda: today_poland.replace(month=int(re.match(r'^(\d{1,2})/(\d{1,2})$', date_str).group(1)), day=int(re.match(r'^(\d{1,2})/(\d{1,2})$', date_str).group(2)))),
            (r'^(\d{4})-(\d{1,2})-(\d{1,2})$', lambda: datetime(int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(1)), int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(2)), int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(3)))),
            (r'^(\d{1,2})-(\d{1,2})-(\d{4})$', lambda: datetime(int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(3)), int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(1)), int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(2))))
        ]
        
        target_date = None
        for pattern, parser in date_patterns:
            if re.match(pattern, date_str.lower()):
                target_date = parser()
                break
        
        if not target_date:
            return None
        
        # Parse time
        time_match = re.match(r'^(\d{1,2}):(\d{2})(?:\s*(am|pm))?$', time_str.lower())
        if not time_match:
            return None
        
        hour = int(time_match.group(1))
        minute = int(time_match.group(2))
        period = time_match.group(3)
        
        # Convert 12-hour to 24-hour format
        if period:
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0
        
        # Create timezone-aware datetime for Poland
        tz = pytz.timezone(POLAND_TZ)
        local_dt = tz.localize(target_date.replace(hour=hour, minute=minute, second=0, microsecond=0))
        
        # Convert to UTC
        utc_dt = local_dt.astimezone(pytz.UTC)
        
        return utc_dt
        
    except Exception as e:
        logger.error(f"Error parsing date/time: {e}")
        return None


# Placeholder for user_id - will be injected when agent processes query
CURRENT_USER_ID = None


@tool(show_result=False, requires_confirmation=False)
def create_reminder(message_text: str, date: str, time: str) -> dict:
    """
    Create a new reminder for a specific date and time.
    
    Args:
        message_text (str): The message to send when the reminder triggers
        date (str): Date in format: 'today', 'tomorrow', 'MM/DD', 'YYYY-MM-DD', or 'DD-MM-YYYY'
        time (str): Time in format: 'HH:MM' or 'HH:MM AM/PM'
    
    Returns:
        dict: Result data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}
        
        # Parse date and time using Poland timezone
        scheduled_time_utc = parse_date_time(date, time)
        if not scheduled_time_utc:
            return {"success": False, "error": "Invalid date or time format"}
        
        # Check if time is in the past
        if scheduled_time_utc <= datetime.now(pytz.UTC):
            return {"success": False, "error": "Cannot create reminder for past time"}
        
        # Create reminder
        reminder_id = SimpleReminderManager.create_reminder(
            user_id=user_id,
            message_text=message_text,
            scheduled_time_utc=scheduled_time_utc
        )
        
        if reminder_id:
            # Schedule the reminder synchronously
            try:
                import asyncio
                loop = asyncio.get_event_loop()
                loop.create_task(reminder_scheduler.schedule_reminder(reminder_id, scheduled_time_utc))
            except Exception as e:
                logger.error(f"Failed to schedule reminder {reminder_id}: {e}")
            
            # Convert back to Poland timezone for display
            poland_tz = pytz.timezone(POLAND_TZ)
            local_time = scheduled_time_utc.astimezone(poland_tz)
            
            return {
                "success": True, 
                "reminder_id": reminder_id,
                "message": message_text,
                "scheduled_time": local_time.strftime('%Y-%m-%d %H:%M %Z')
            }
        else:
            return {"success": False, "error": "Failed to create reminder"}
            
    except Exception as e:
        logger.error(f"Error creating reminder: {e}")
        return {"success": False, "error": str(e)}


@tool(show_result=False, cache_results=True, cache_ttl=60)
def list_reminders(status: str = "pending") -> dict:
    """
    List your reminders filtered by status.
    
    Args:
        status (str): Filter by status - 'pending', 'completed', 'cancelled', or 'all'
    
    Returns:
        dict: Reminders data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}
        
        poland_tz = pytz.timezone(POLAND_TZ)
        reminders = SimpleReminderManager.get_user_reminders(user_id, status)
        
        reminder_list = []
        for reminder in reminders:
            local_time = reminder.scheduled_time_utc.astimezone(poland_tz)
            reminder_list.append({
                "id": reminder.id,
                "message": reminder.message_text,
                "scheduled_time": local_time.strftime('%Y-%m-%d %H:%M %Z'),
                "is_sent": reminder.is_sent,
                "is_cancelled": reminder.is_cancelled
            })
        
        return {
            "success": True,
            "status": status,
            "count": len(reminder_list),
            "reminders": reminder_list
        }
        
    except Exception as e:
        logger.error(f"Error listing reminders: {e}")
        return {"success": False, "error": str(e)}


@tool(show_result=False, requires_confirmation=True)
def delete_reminder(reminder_id: int) -> dict:
    """
    Delete a reminder by its ID.
    
    Args:
        reminder_id (int): The ID of the reminder to delete
    
    Returns:
        dict: Result data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}
        
        # Check if reminder exists and belongs to user
        reminder = SimpleReminderManager.get_reminder(reminder_id)
        if not reminder:
            return {"success": False, "error": f"Reminder #{reminder_id} not found"}
        
        if reminder.user_id != user_id:
            return {"success": False, "error": "Permission denied"}
        
        # Cancel scheduled job first
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            loop.create_task(reminder_scheduler.cancel_reminder(reminder_id))
        except Exception as e:
            logger.error(f"Failed to cancel scheduled reminder {reminder_id}: {e}")
        
        # Delete reminder
        if SimpleReminderManager.delete_reminder(reminder_id):
            return {
                "success": True,
                "reminder_id": reminder_id,
                "message": reminder.message_text
            }
        else:
            return {"success": False, "error": "Failed to delete reminder"}
            
    except Exception as e:
        logger.error(f"Error deleting reminder: {e}")
        return {"success": False, "error": str(e)}


@tool(show_result=False, requires_confirmation=False)
def cancel_reminder(reminder_id: int) -> dict:
    """
    Cancel a reminder (keeps it in history but won't send).
    
    Args:
        reminder_id (int): The ID of the reminder to cancel
    
    Returns:
        dict: Result data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}
        
        # Check if reminder exists and belongs to user
        reminder = SimpleReminderManager.get_reminder(reminder_id)
        if not reminder:
            return {"success": False, "error": f"Reminder #{reminder_id} not found"}
        
        if reminder.user_id != user_id:
            return {"success": False, "error": "Permission denied"}
        
        if reminder.is_sent:
            return {"success": False, "error": "Reminder already sent"}
        
        if reminder.is_cancelled:
            return {"success": False, "error": "Reminder already cancelled"}
        
        # Cancel scheduled job first
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            loop.create_task(reminder_scheduler.cancel_reminder(reminder_id))
        except Exception as e:
            logger.error(f"Failed to cancel scheduled reminder {reminder_id}: {e}")
        
        # Cancel reminder
        if SimpleReminderManager.cancel_reminder(reminder_id):
            return {
                "success": True,
                "reminder_id": reminder_id,
                "message": reminder.message_text
            }
        else:
            return {"success": False, "error": "Failed to cancel reminder"}
            
    except Exception as e:
        logger.error(f"Error cancelling reminder: {e}")
        return {"success": False, "error": str(e)}


# Export reminder tools (no timezone tools)
SIMPLE_REMINDER_TOOLS = [
    create_reminder,
    list_reminders,
    delete_reminder,
    cancel_reminder
]