from agno.tools import tool
from datetime import datetime, timedelta
from typing import Optional
import pytz
import re
import logging

from src.utils.simple_scheduler import create_reminder, get_user_reminders, delete_reminder

logger = logging.getLogger(__name__)

# Use Poland timezone for all users
POLAND_TZ = "Europe/Warsaw"

def parse_date_time(date_str: str, time_str: str) -> Optional[datetime]:
    """Parse date and time strings into timezone-aware datetime."""
    try:
        # Parse date - use Poland timezone for "today"
        poland_tz = pytz.timezone(POLAND_TZ)
        today_poland = datetime.now(poland_tz).replace(tzinfo=None)  # Remove timezone for date manipulation
        
        date_patterns = [
            (r'^today$', lambda: today_poland),
            (r'^tomorrow$', lambda: today_poland + timedelta(days=1)),
            (r'^(\d{1,2})/(\d{1,2})$', lambda: today_poland.replace(month=int(re.match(r'^(\d{1,2})/(\d{1,2})$', date_str).group(1)), day=int(re.match(r'^(\d{1,2})/(\d{1,2})$', date_str).group(2)))),
            (r'^(\d{4})-(\d{1,2})-(\d{1,2})$', lambda: datetime(int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(1)), int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(2)), int(re.match(r'^(\d{4})-(\d{1,2})-(\d{1,2})$', date_str).group(3)))),
            (r'^(\d{1,2})-(\d{1,2})-(\d{4})$', lambda: datetime(int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(3)), int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(1)), int(re.match(r'^(\d{1,2})-(\d{1,2})-(\d{4})$', date_str).group(2))))
        ]
        
        target_date = None
        for pattern, parser in date_patterns:
            if re.match(pattern, date_str.lower()):
                target_date = parser()
                break
        
        if not target_date:
            return None
        
        # Parse time
        time_match = re.match(r'^(\d{1,2}):(\d{2})(?:\s*(am|pm))?$', time_str.lower())
        if not time_match:
            return None
        
        hour = int(time_match.group(1))
        minute = int(time_match.group(2))
        period = time_match.group(3)
        
        # Convert 12-hour to 24-hour format
        if period:
            if period == 'pm' and hour != 12:
                hour += 12
            elif period == 'am' and hour == 12:
                hour = 0
        
        # Create timezone-aware datetime for Poland
        tz = pytz.timezone(POLAND_TZ)
        local_dt = tz.localize(target_date.replace(hour=hour, minute=minute, second=0, microsecond=0))
        
        # Convert to UTC
        utc_dt = local_dt.astimezone(pytz.UTC)
        
        return utc_dt
        
    except Exception as e:
        logger.error(f"Error parsing date/time: {e}")
        return None


# Placeholder for user_id - will be injected when agent processes query
CURRENT_USER_ID = None


@tool(show_result=False, requires_confirmation=False)
def create_reminder_tool(message_text: str, date: str, time: str) -> dict:
    """
    Create a new reminder for a specific date and time.

    Args:
        message_text (str): The message to send when the reminder triggers
        date (str): Date in format: 'today', 'tomorrow', 'MM/DD', 'YYYY-MM-DD', or 'DD-MM-YYYY'
        time (str): Time in format: 'HH:MM' or 'HH:MM AM/PM'

    Returns:
        dict: Result data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}

        # Parse date and time using Poland timezone
        scheduled_time_utc = parse_date_time(date, time)
        if not scheduled_time_utc:
            return {"success": False, "error": "Invalid date or time format"}

        # Check if time is in the past
        if scheduled_time_utc <= datetime.now(pytz.UTC):
            return {"success": False, "error": "Cannot create reminder for past time"}

        # Create reminder using simple scheduler
        reminder_id = create_reminder(
            user_id=user_id,
            message_text=message_text,
            scheduled_time_utc=scheduled_time_utc
        )

        if reminder_id:
            # Convert back to Poland timezone for display
            poland_tz = pytz.timezone(POLAND_TZ)
            local_time = scheduled_time_utc.astimezone(poland_tz)

            result = {
                "success": True,
                "reminder_id": reminder_id,
                "message": message_text,
                "scheduled_time": local_time.strftime('%Y-%m-%d %H:%M %Z')
            }

            logger.info(f"✅ Created reminder {reminder_id} for {local_time.strftime('%Y-%m-%d %H:%M %Z')}")
            return result
        else:
            return {"success": False, "error": "Failed to create reminder"}

    except Exception as e:
        logger.error(f"❌ Error creating reminder: {e}")
        return {"success": False, "error": str(e)}


@tool(show_result=False, cache_results=True, cache_ttl=60)
def list_reminders_tool(status: str = "pending") -> dict:
    """
    List your reminders filtered by status.

    Args:
        status (str): Filter by status - 'pending', 'completed', or 'all'

    Returns:
        dict: Reminders data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}

        poland_tz = pytz.timezone(POLAND_TZ)
        reminders_data = get_user_reminders(user_id, status)

        reminder_list = []
        for reminder in reminders_data:
            scheduled_time = datetime.fromisoformat(reminder['scheduled_time_utc'])
            local_time = scheduled_time.astimezone(poland_tz)
            reminder_list.append({
                "id": reminder['id'],
                "message": reminder['message_text'],
                "scheduled_time": local_time.strftime('%Y-%m-%d %H:%M %Z'),
                "is_sent": bool(reminder['is_sent'])
            })

        return {
            "success": True,
            "status": status,
            "count": len(reminder_list),
            "reminders": reminder_list
        }

    except Exception as e:
        logger.error(f"❌ Error listing reminders: {e}")
        return {"success": False, "error": str(e)}


@tool(show_result=False, requires_confirmation=True)
def delete_reminder_tool(reminder_id: int) -> dict:
    """
    Delete a reminder by its ID.

    Args:
        reminder_id (int): The ID of the reminder to delete

    Returns:
        dict: Result data
    """
    try:
        user_id = CURRENT_USER_ID
        if not user_id:
            return {"success": False, "error": "User context not available"}

        # Get reminder to check ownership and get message text
        reminders_data = get_user_reminders(user_id, "all")
        reminder_to_delete = None

        for reminder in reminders_data:
            if reminder['id'] == reminder_id:
                reminder_to_delete = reminder
                break

        if not reminder_to_delete:
            return {"success": False, "error": f"Reminder #{reminder_id} not found or access denied"}

        # Delete reminder
        if delete_reminder(reminder_id):
            result = {
                "success": True,
                "reminder_id": reminder_id,
                "message": reminder_to_delete['message_text']
            }
            logger.info(f"✅ Deleted reminder {reminder_id}")
            return result
        else:
            return {"success": False, "error": "Failed to delete reminder"}

    except Exception as e:
        logger.error(f"❌ Error deleting reminder: {e}")
        return {"success": False, "error": str(e)}





# Export simple reminder tools
SIMPLE_REMINDER_TOOLS = [
    create_reminder_tool,
    list_reminders_tool,
    delete_reminder_tool
]