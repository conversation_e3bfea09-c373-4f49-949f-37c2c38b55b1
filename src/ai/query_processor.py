import asyncio
import logging
import time
from typing import List, Optional

from agno.agent import Agent
from agno.media import Image, File
from agno.run.response import Run<PERSON><PERSON>
from agno.tools import FunctionCall
from anthropic import APIStatusError
from telegram.constants import ChatAction
from telegram.ext import ContextTypes

from src.messaging import MessageFormatter
from src.messaging import MessageProcessor
from src.ai.tools import simple_reminders

FAST_UPDATE_CHARS = 150  # First N characters update quickly
FAST_UPDATE_INTERVAL = 0.5  # Update interval for first N characters
NORMAL_UPDATE_INTERVAL = 2.0  # Update interval for the rest of the message
TOKENS_LIMIT_TO_SUMMARIZE = 4000  # Limit for summarizing the chat history

logger = logging.getLogger(__name__)


class QueryProcessor:
    agent: Agent

    def __init__(self, agent: Agent):
        self.agent = agent

    async def process_query(self, query: str, chat_id: int, context: ContextTypes.DEFAULT_TYPE, images: Optional[List[Image]] = None, files: Optional[List[File]] = None, reply_to_message_id: Optional[int] = None, user_id: int = None) -> None:
        """
        Process a user query and stream the results back.
        
        Args:
            query: The text query from the user
            chat_id: The chat ID for the conversation
            context: The Telegram context
            images: Optional list of Image objects to include with the query
            files: Optional list of File objects to include with the query
            reply_to_message_id: Optional message ID to reply to
            user_id: User ID for reminder tools
        """
        logger.info(f"Processing query for chat_id {chat_id}: {query[:100]}...")
        if images:
            logger.info(f"Query includes {len(images)} images")
        if files:
            logger.info(f"Query includes {len(files)} files")

        message_handler = MessageProcessor(context, chat_id, reply_to_message_id)
        formatter = MessageFormatter()

        try:
            await message_handler.send_initial_message("💭")

            if not message_handler.message_id:
                return

            await self._process_stream(query, chat_id, context, message_handler, formatter, images, files, reply_to_message_id, user_id)

        except APIStatusError as e:
            if e.status_code == 429:
                await message_handler.send_error_message("Rate limit exceeded. Please try again later.")
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)

            # TODO detect if error is ApiErrorType, and handle it better

            await message_handler.send_error_message(f"An unexpected error occurred: {type(e).__name__}")

    async def _process_stream(self, query: str, chat_id: int, context: ContextTypes.DEFAULT_TYPE,
                              message_processor: MessageProcessor, formatter: MessageFormatter,
                              images: Optional[List[Image]] = None, files: Optional[List[File]] = None, reply_to_message_id: Optional[int] = None, user_id: int = None) -> None:
        last_update_time = 0
        current_text_buffer = ""  # Buffer for accumulating text
        processed_tool_ids = set()  # Track tool calls to prevent duplicates
        total_text_length = 0  # Track total text length
        need_summarize = False

        # Inject user_id for reminder tools
        if user_id:
            simple_reminders.CURRENT_USER_ID = user_id

        await context.bot.send_chat_action(chat_id, ChatAction.TYPING)

        # Extract session_id and user_id from the agent
        # These were set when the agent was created in get_basic_agent()
        session_id = getattr(self.agent, 'session_id', None) or f"fallback_{chat_id}"
        user_id = getattr(self.agent, 'user_id', None) or str(chat_id)
        
        # Combine images and files for the agent call
        media_items = []
        if images:
            media_items.extend(images)
        if files:
            media_items.extend(files)
        
        # Prepare arguments for arun
        arun_kwargs = {
            "stream": True,
            "stream_intermediate_steps": True,
            "user_id": user_id,
            "session_id": session_id,
        }
        
        # Add media parameters only if they exist
        if images:
            arun_kwargs["images"] = images
        if files:
            arun_kwargs["files"] = files
        
        logger.info(f"🚀 Calling agent.arun with: images={len(images) if images else 0}, files={len(files) if files else 0}")
        
        response_stream = await self.agent.arun(query, **arun_kwargs)

        async for chunk in response_stream:
            current_time = time.time()
            logger.debug(
                f"Chat {chat_id} Chunk: {chunk.event} | {chunk.content_type} | Data: {str(chunk.content)[:50]}... | Extra: {chunk.extra_data}")

            should_update_message = False
            is_tool_event = False

            if chunk.event == RunEvent.run_response and chunk.content_type == "str" and chunk.content:
                # Accumulate text in buffer
                current_text_buffer += chunk.content
                total_text_length += len(chunk.content)

                # Determine update interval based on total text length
                update_interval = FAST_UPDATE_INTERVAL if total_text_length <= FAST_UPDATE_CHARS else NORMAL_UPDATE_INTERVAL

                # Check if it's time to update
                if current_time - last_update_time >= update_interval:
                    formatter.add_text(current_text_buffer)
                    current_text_buffer = ""
                    should_update_message = True

            elif chunk.event == RunEvent.tool_call_started:
                logger.info(f"Chat {chat_id}: Tool call started event.")

                # Flush any pending text before adding tool
                if current_text_buffer:
                    formatter.add_text(current_text_buffer)
                    current_text_buffer = ""

                if chunk.tools:
                    for tool in chunk.tools:  # type: FunctionCall
                        tool_name = tool.tool_name if hasattr(tool, 'tool_name') else 'Unknown Tool'
                        tool_call_id = tool.tool_call_id if hasattr(tool, 'tool_call_id') else f"{tool_name}-{len(processed_tool_ids)}"

                        # Only process if we haven't seen this exact tool call
                        tool_key = f"{tool_call_id}:{True}"
                        if tool_key not in processed_tool_ids:
                            processed_tool_ids.add(tool_key)

                            # Create and add tool component
                            formatter.add_tool(tool_name, tool_call_id)
                            should_update_message = True
                            is_tool_event = True  # Tool events update immediately

            elif chunk.event == RunEvent.tool_call_completed:
                logger.info(f"Chat {chat_id}: Tool call completed event.")

                # Flush any pending text before updating tool
                if current_text_buffer:
                    formatter.add_text(current_text_buffer)
                    current_text_buffer = ""

                if chunk.tools:
                    for tool in chunk.tools:  # type: FunctionCall
                        tool_name = tool.tool_name if hasattr(tool, 'tool_name') else 'Unknown Tool'
                        tool_call_id = tool.tool_call_id if hasattr(tool, 'tool_call_id') else f"{tool_name}-{len(processed_tool_ids)}"
                        is_error = getattr(tool, 'tool_call_error', False)

                        # Only process if we haven't seen this exact tool completion
                        tool_key = f"{tool_call_id}:completed"
                        if tool_key not in processed_tool_ids:
                            processed_tool_ids.add(tool_key)

                            # Handle reminder scheduling if this is a reminder tool
                            await self._handle_reminder_tool_result(tool, user_id)

                            # Update tool status
                            formatter.update_tool(tool_call_id, True, is_error)
                            should_update_message = True
                            is_tool_event = True  # Tool events update immediately

            elif chunk.event == RunEvent.run_completed:
                total_tokens = sum(message.metrics.total_tokens for message in chunk.messages)

                print(f"Chat {chat_id} | Tokens: {total_tokens}")

                # if total_tokens > TOKENS_LIMIT_TO_SUMMARIZE:
                #     need_summarize = True

            # Update the message if needed
            if should_update_message:
                formatted_content = formatter.format_message()
                await message_processor.update_message(formatted_content)
                last_update_time = time.time()

                # Keep typing indicator active
                if is_tool_event or total_text_length % 500 == 0:
                    await context.bot.send_chat_action(chat_id, ChatAction.TYPING)

            await asyncio.sleep(0.05)

        # Handle any leftover text at the end
        if current_text_buffer:
            formatter.add_text(current_text_buffer)

        formatted_content = formatter.format_message()
        final_content = formatted_content if formatted_content else "No response generated 🤨"

        await message_processor.update_message(final_content)
        
        # Clear user_id after processing to prevent leaking to other sessions
        simple_reminders.CURRENT_USER_ID = None

        # if True:
            # session_summary = self.agent.get_session_summary(
            #     user_id=str(chat_id), session_id=f"private_{chat_id}",
            # )
            #
            # print(f"Chat {chat_id} | Session summary: {session_summary}")

    async def _handle_reminder_tool_result(self, tool, user_id: int):
        """Handle reminder tool results to schedule or cancel reminders."""
        try:
            tool_name = getattr(tool, 'tool_name', '')
            tool_result = getattr(tool, 'tool_result', None)

            if not tool_result or not isinstance(tool_result, dict):
                return

            logger.info(f"🔧 Processing tool result for {tool_name}: {tool_result}")

            # Handle reminder creation
            if '_schedule_reminder' in tool_result:
                schedule_info = tool_result['_schedule_reminder']
                reminder_id = schedule_info.get('reminder_id')
                scheduled_time_utc = schedule_info.get('scheduled_time_utc')

                if reminder_id and scheduled_time_utc:
                    from src.utils.scheduler import reminder_scheduler
                    success = await reminder_scheduler.schedule_reminder(reminder_id, scheduled_time_utc)
                    logger.info(f"📅 Scheduled reminder {reminder_id}: {success}")

            # Handle reminder cancellation
            elif '_cancel_reminder' in tool_result:
                cancel_info = tool_result['_cancel_reminder']
                reminder_id = cancel_info.get('reminder_id')

                if reminder_id:
                    from src.utils.scheduler import reminder_scheduler
                    success = await reminder_scheduler.cancel_reminder(reminder_id)
                    logger.info(f"🚫 Cancelled reminder {reminder_id}: {success}")

        except Exception as e:
            logger.error(f"❌ Error handling reminder tool result: {e}", exc_info=True)
