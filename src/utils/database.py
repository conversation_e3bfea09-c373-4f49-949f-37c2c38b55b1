import sqlite3
import logging
from typing import Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DatabaseManager:
    def __init__(self, db_path: str = ".tmp/agent.db"):
        self.db_path = db_path
    
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with proper settings."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return cursor.fetchone() is not None
    
    def run_migrations(self):
        """Run database migrations for reminder system."""
        logger.info("Checking and running database migrations...")

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Check if reminders table exists and has correct structure
            if not self.table_exists("reminders"):
                logger.info("Creating reminders table...")
                cursor.execute("""
                    CREATE TABLE reminders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        message_text TEXT NOT NULL,
                        scheduled_time_utc TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        is_sent INTEGER DEFAULT 0
                    )
                """)

                # Create indexes for better performance
                cursor.execute("CREATE INDEX idx_reminders_user_id ON reminders(user_id)")
                cursor.execute("CREATE INDEX idx_reminders_scheduled_time ON reminders(scheduled_time_utc)")
                cursor.execute("CREATE INDEX idx_reminders_status ON reminders(is_sent)")

                logger.info("✅ Reminders table created with indexes")
            else:
                # Check if table structure needs updating
                cursor.execute("PRAGMA table_info(reminders)")
                columns = [row[1] for row in cursor.fetchall()]

                # Migration: Remove old columns if they exist
                if 'is_cancelled' in columns or 'scheduler_job_id' in columns:
                    logger.info("Updating reminders table structure...")

                    # Create new table with correct structure
                    cursor.execute("""
                        CREATE TABLE reminders_new (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            user_id INTEGER NOT NULL,
                            message_text TEXT NOT NULL,
                            scheduled_time_utc TEXT NOT NULL,
                            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                            is_sent INTEGER DEFAULT 0
                        )
                    """)

                    # Copy data from old table (only non-cancelled reminders)
                    if 'is_cancelled' in columns:
                        cursor.execute("""
                            INSERT INTO reminders_new (id, user_id, message_text, scheduled_time_utc, created_at, is_sent)
                            SELECT id, user_id, message_text, scheduled_time_utc, created_at,
                                   CASE WHEN is_sent = 1 OR is_sent = 'TRUE' THEN 1 ELSE 0 END
                            FROM reminders
                            WHERE is_cancelled = 0 OR is_cancelled = 'FALSE' OR is_cancelled IS NULL
                        """)
                    else:
                        cursor.execute("""
                            INSERT INTO reminders_new (id, user_id, message_text, scheduled_time_utc, created_at, is_sent)
                            SELECT id, user_id, message_text, scheduled_time_utc, created_at,
                                   CASE WHEN is_sent = 1 OR is_sent = 'TRUE' THEN 1 ELSE 0 END
                            FROM reminders
                        """)

                    # Replace old table
                    cursor.execute("DROP TABLE reminders")
                    cursor.execute("ALTER TABLE reminders_new RENAME TO reminders")

                    # Recreate indexes
                    cursor.execute("CREATE INDEX idx_reminders_user_id ON reminders(user_id)")
                    cursor.execute("CREATE INDEX idx_reminders_scheduled_time ON reminders(scheduled_time_utc)")
                    cursor.execute("CREATE INDEX idx_reminders_status ON reminders(is_sent)")

                    logger.info("✅ Reminders table structure updated")
                else:
                    logger.info("✅ Reminders table structure is up to date")

            # Clean up old APScheduler table if it exists
            if self.table_exists("apscheduler_jobs"):
                cursor.execute("DROP TABLE apscheduler_jobs")
                logger.info("✅ Cleaned up old APScheduler table")

            conn.commit()
            logger.info("Database migrations completed successfully")


# Global database manager instance
db_manager = DatabaseManager()