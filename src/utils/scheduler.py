import logging
import asyncio
from datetime import datetime
from typing import Optional, Callable
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import Async<PERSON><PERSON>xecutor
from apscheduler.triggers.date import DateTrigger

from src.ai.simple_models import SimpleReminderManager

logger = logging.getLogger(__name__)

# Global callback for sending messages - set by bot
_message_callback: Optional[Callable] = None


async def send_reminder_job(reminder_id: int):
    """Standalone function to send a reminder - can be serialized by APScheduler."""
    try:
        logger.info(f"🔔 Sending reminder {reminder_id}")
        
        # Get reminder details
        reminder = SimpleReminderManager.get_reminder(reminder_id)
        if not reminder:
            logger.error(f"Reminder {reminder_id} not found")
            return
        
        if reminder.is_sent or reminder.is_cancelled:
            logger.warning(f"Reminder {reminder_id} already sent or cancelled")
            return
        
        # Send message via callback - just the saved text, nothing else
        if _message_callback:
            await _message_callback(
                user_id=reminder.user_id,
                message=reminder.message_text
            )
        else:
            logger.warning("No message callback set for reminder delivery")
        
        # Mark reminder as sent
        SimpleReminderManager.mark_as_sent(reminder_id)
        logger.info(f"✅ Reminder {reminder_id} sent successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to send reminder {reminder_id}: {e}")


class ReminderScheduler:
    def __init__(self, db_path: str = ".tmp/agent.db"):
        """Initialize the reminder scheduler."""
        self.db_path = db_path
        self.scheduler: Optional[AsyncIOScheduler] = None
        
        # Configure APScheduler
        jobstores = {
            'default': SQLAlchemyJobStore(url=f'sqlite:///{db_path}', tablename='apscheduler_jobs')
        }
        
        executors = {
            'default': AsyncIOExecutor()
        }
        
        job_defaults = {
            'coalesce': True,  # Combine missed executions
            'max_instances': 3,  # Max concurrent jobs
            'misfire_grace_time': 300  # 5 minutes grace period
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=pytz.UTC
        )
    
    def set_message_callback(self, callback: Callable):
        """Set the callback function for sending messages."""
        global _message_callback
        _message_callback = callback
    
    async def start(self):
        """Start the scheduler."""
        if self.scheduler and not self.scheduler.running:
            self.scheduler.start()
            logger.info("✅ Reminder scheduler started")
            
            # Schedule any existing pending reminders
            await self.schedule_pending_reminders()
            
            # Log current jobs
            jobs = self.scheduler.get_jobs()
            logger.info(f"📋 Active jobs after startup: {len(jobs)}")
            for job in jobs:
                logger.info(f"  Job {job.id}: next run {job.next_run_time}")
        else:
            logger.warning("⚠️ Scheduler already running or not initialized")
    
    async def stop(self):
        """Stop the scheduler."""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown(wait=True)
            logger.info("🛑 Reminder scheduler stopped")
    
    async def schedule_reminder(self, reminder_id: int, scheduled_time_utc: datetime) -> bool:
        """Schedule a reminder for delivery."""
        try:
            if not self.scheduler or not self.scheduler.running:
                logger.error(f"❌ Scheduler not running when trying to schedule reminder {reminder_id}")
                return False
                
            job_id = f"reminder_{reminder_id}"
            
            # Check if job already exists
            existing_job = self.scheduler.get_job(job_id)
            if existing_job:
                logger.warning(f"Job {job_id} already exists, removing old one")
                self.scheduler.remove_job(job_id)
            
            # Schedule the job
            job = self.scheduler.add_job(
                send_reminder_job,
                trigger=DateTrigger(run_date=scheduled_time_utc),
                args=[reminder_id],
                id=job_id,
                replace_existing=True,
                misfire_grace_time=300  # 5 minutes
            )
            
            # Update reminder with job ID
            SimpleReminderManager.update_reminder(reminder_id, scheduler_job_id=job_id)
            
            logger.info(f"📅 Scheduled reminder {reminder_id} for {scheduled_time_utc}")
            logger.info(f"📋 Job {job_id} next run: {job.next_run_time}")
            
            # List all current jobs
            all_jobs = self.scheduler.get_jobs()
            logger.info(f"📋 Total active jobs: {len(all_jobs)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule reminder {reminder_id}: {e}")
            return False
    
    async def cancel_reminder(self, reminder_id: int) -> bool:
        """Cancel a scheduled reminder."""
        try:
            job_id = f"reminder_{reminder_id}"
            
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"🚫 Cancelled scheduled reminder {reminder_id}")
                return True
            else:
                logger.warning(f"No scheduled job found for reminder {reminder_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to cancel reminder {reminder_id}: {e}")
            return False
    
    async def reschedule_reminder(self, reminder_id: int, new_scheduled_time_utc: datetime) -> bool:
        """Reschedule an existing reminder."""
        await self.cancel_reminder(reminder_id)
        return await self.schedule_reminder(reminder_id, new_scheduled_time_utc)
    
    async def schedule_pending_reminders(self):
        """Schedule all pending reminders on startup."""
        logger.info("📋 Scheduling pending reminders...")

        try:
            # Get all pending reminders from database
            from src.utils.database import db_manager
            current_time = datetime.now(pytz.UTC)
            logger.info(f"🕐 Current UTC time: {current_time.isoformat()}")

            with db_manager.get_connection() as conn:
                cursor = conn.cursor()

                # First, let's see all reminders for debugging
                cursor.execute("""
                    SELECT id, scheduled_time_utc, is_sent, is_cancelled, message_text FROM reminders
                    WHERE is_sent = FALSE AND is_cancelled = FALSE
                """)
                all_pending = cursor.fetchall()
                logger.info(f"📊 Found {len(all_pending)} total pending reminders:")
                for row in all_pending:
                    scheduled_time = datetime.fromisoformat(row['scheduled_time_utc'])
                    is_future = scheduled_time > current_time
                    logger.info(f"  ID {row['id']}: {scheduled_time.isoformat()} ({'FUTURE' if is_future else 'PAST'}) - {row['message_text'][:50]}...")

                # Now get only future reminders
                cursor.execute("""
                    SELECT id, scheduled_time_utc FROM reminders
                    WHERE is_sent = FALSE AND is_cancelled = FALSE
                    AND scheduled_time_utc > ?
                """, (current_time.isoformat(),))

                pending_reminders = cursor.fetchall()
                logger.info(f"🔮 Found {len(pending_reminders)} future reminders to schedule")

            scheduled_count = 0
            for row in pending_reminders:
                reminder_id = row['id']
                scheduled_time = datetime.fromisoformat(row['scheduled_time_utc'])

                logger.info(f"📅 Attempting to schedule reminder {reminder_id} for {scheduled_time.isoformat()}")
                if await self.schedule_reminder(reminder_id, scheduled_time):
                    scheduled_count += 1
                    logger.info(f"✅ Successfully scheduled reminder {reminder_id}")
                else:
                    logger.error(f"❌ Failed to schedule reminder {reminder_id}")

            logger.info(f"✅ Scheduled {scheduled_count} pending reminders")

        except Exception as e:
            logger.error(f"❌ Failed to schedule pending reminders: {e}", exc_info=True)
    
    
    def get_scheduler_status(self) -> dict:
        """Get scheduler status information."""
        if not self.scheduler:
            return {'running': False, 'jobs': 0}
        
        jobs = self.scheduler.get_jobs()
        return {
            'running': self.scheduler.running,
            'jobs': len(jobs),
            'job_details': [
                {
                    'id': job.id,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None
                }
                for job in jobs
            ]
        }
    
    async def test_immediate_reminder(self, message: str, seconds: int = 30) -> bool:
        """Test function to create an immediate reminder."""
        try:
            from datetime import timedelta
            test_time = datetime.now(pytz.UTC) + timedelta(seconds=seconds)

            logger.info(f"🧪 Creating test reminder for {test_time.isoformat()} (in {seconds} seconds)")

            # Create test reminder
            reminder_id = SimpleReminderManager.create_reminder(
                user_id=1730029726,  # Your user ID
                message_text=message,
                scheduled_time_utc=test_time
            )

            if reminder_id:
                success = await self.schedule_reminder(reminder_id, test_time)
                logger.info(f"🧪 Test reminder {reminder_id} scheduled for {test_time}: {success}")

                # Log current scheduler status
                jobs = self.scheduler.get_jobs() if self.scheduler else []
                logger.info(f"📋 Current active jobs: {len(jobs)}")
                for job in jobs:
                    logger.info(f"  Job {job.id}: next run {job.next_run_time}")

                return success
            return False

        except Exception as e:
            logger.error(f"❌ Test reminder failed: {e}", exc_info=True)
            return False

    async def create_test_future_reminder(self, minutes: int = 2) -> bool:
        """Create a test reminder for the near future and schedule it."""
        try:
            from datetime import timedelta
            test_time = datetime.now(pytz.UTC) + timedelta(minutes=minutes)

            logger.info(f"🧪 Creating test future reminder for {test_time.isoformat()} (in {minutes} minutes)")

            # Create test reminder
            reminder_id = SimpleReminderManager.create_reminder(
                user_id=1730029726,  # Your user ID
                message_text=f"🧪 Test reminder created at {datetime.now(pytz.UTC).strftime('%H:%M:%S')}",
                scheduled_time_utc=test_time
            )

            if reminder_id:
                success = await self.schedule_reminder(reminder_id, test_time)
                logger.info(f"🧪 Test future reminder {reminder_id} created and scheduled: {success}")
                return success
            return False

        except Exception as e:
            logger.error(f"❌ Test future reminder failed: {e}", exc_info=True)
            return False


# Global scheduler instance
reminder_scheduler = ReminderScheduler()